<!-- Manufacturer Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <a href="{{ cancel }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
                <div class="w-8 h-8 flex items-center justify-center">
                    <i class="ri-arrow-left-line ri-lg"></i>
                </div>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">
                    {% if manufacturer_id %}
                        Редактиране на производител
                    {% else %}
                        Добавяне на производител
                    {% endif %}
                </h1>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            {% if manufacturer_id and delete_url %}
            <button type="button" id="button-delete" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors">
                <i class="ri-delete-bin-line mr-2"></i>
                Изтрий
            </button>
            {% endif %}
            <button type="button" id="button-cancel" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors">
                <i class="ri-close-line mr-2"></i>
                Отказ
            </button>
            <button type="submit" form="manufacturer-form" id="button-save" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <i class="ri-save-line mr-2"></i>
                Запази
            </button>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <form id="manufacturer-form" action="{{ action }}" method="post" class="space-y-6">
        <input type="hidden" id="manufacturer-id" name="manufacturer_id" value="{{ manufacturer_id }}">
        
        <div class="max-w-4xl">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                
                <!-- Form Content -->
                <div class="p-6">
                    
                    <!-- Basic Information Section -->
                    <div class="space-y-6">
                        
                        <!-- Manufacturer Name -->
                        <div>
                            <label for="input-name" class="block text-sm font-medium text-gray-700 mb-1">
                                Име на производителя <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="input-name" name="name" value="{{ name }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm required"
                                   placeholder="Въведете име на производителя..." autocomplete="off">
                            <p class="text-xs text-gray-500 mt-1">Името на производителя (1-64 символа)</p>
                        </div>

                        <!-- Image Section -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Изображение</label>
                            <div class="flex items-start space-x-4">
                                <div id="image-container" class="flex-shrink-0">
                                    <div class="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer">
                                        {% if image %}
                                            <img id="image-preview" src="{{ thumb }}" alt="Изображение на производителя" class="w-full h-full object-cover rounded-lg">
                                        {% else %}
                                            <div class="text-center">
                                                <i class="ri-image-add-line text-2xl text-gray-400 mb-2"></i>
                                                <p class="text-xs text-gray-500">Кликнете за избор</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <input type="hidden" id="input-image" name="image" value="{{ image }}">
                                    <div class="space-y-2">
                                        <button type="button" id="button-image" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors" data-href="{{ image_manager }}">
                                            <i class="ri-folder-open-line mr-2"></i>
                                            Избери изображение
                                        </button>
                                        {% if image %}
                                        <button type="button" id="button-clear-image" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                            <i class="ri-delete-bin-line mr-2"></i>
                                            Премахни
                                        </button>
                                        {% endif %}
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">Препоръчителен размер: 200x200px. Максимален размер: 2MB</p>
                                    <p class="text-xs text-gray-500">Поддържани формати: JPG, PNG, GIF, WebP</p>
                                </div>
                            </div>
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <label for="input-sort-order" class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                            <input type="number" id="input-sort-order" name="sort_order" value="{{ sort_order }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                                   placeholder="0" min="0">
                            <p class="text-xs text-gray-500 mt-1">По-ниските числа се показват първи</p>
                        </div>

                        <!-- Stores Section -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Магазини</h3>
                            <div class="space-y-2">
                                {% for store in stores %}
                                <label class="flex items-center">
                                    <input type="checkbox" name="manufacturer_store[]" value="{{ store.store_id }}"
                                           {% if store.store_id in manufacturer_store %}checked{% endif %}
                                           class="rounded border-gray-300 text-primary focus:ring-primary mr-3">
                                    <span class="text-sm text-gray-700">{{ store.name }}</span>
                                </label>
                                {% endfor %}
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Изберете в кои магазини да се показва производителят</p>
                        </div>

                        <!-- SEO URL Section -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">SEO URL адреси</h3>
                            
                            <!-- SEO URL Table -->
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Магазин</th>
                                            {% for language in languages %}
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <div class="flex items-center">
                                                    <div class="w-6 h-4 flex items-center justify-center {{ language.css }} rounded text-xs font-medium text-white mr-2">
                                                        {{ language.code|upper|slice(0, 2) }}
                                                    </div>
                                                    {{ language.name }}
                                                </div>
                                            </th>
                                            {% endfor %}
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for store in stores %}
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ store.name }}
                                            </td>
                                            {% for language in languages %}
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <div class="flex items-center space-x-2">
                                                    <input type="text" name="manufacturer_seo_url[{{ store.store_id }}][{{ language.language_id }}]"
                                                           value="{{ manufacturer_seo_url[store.store_id][language.language_id] }}"
                                                           class="flex-1 px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-1 focus:ring-primary/20 focus:border-primary"
                                                           placeholder="seo-url">
                                                    <button type="button" class="btn-generate-seo text-blue-600 hover:text-blue-800 transition-colors" 
                                                            data-store-id="{{ store.store_id }}" data-language-id="{{ language.language_id }}" title="Генерирай SEO URL">
                                                        <i class="ri-magic-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            {% endfor %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">SEO URL адресите трябва да бъдат уникални и да съдържат само букви, цифри, тире и долни черти</p>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</main>

<!-- Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
        <span class="text-gray-700">Запазване...</span>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<!-- Custom Styles -->
<style>
/* Image container styles */
#image-container {
    position: relative;
}

#image-container.drag-over {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

#image-preview {
    display: block;
}

/* Form validation styles */
.required {
    position: relative;
}

.is-invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #ef4444;
}

.is-valid {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #10b981;
}

/* SEO URL table styles */
.seo-url-table {
    font-size: 0.875rem;
}

.seo-url-table input {
    min-width: 120px;
}

.btn-generate-seo {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Loading states */
.btn-loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .seo-url-table {
        font-size: 0.75rem;
    }

    .seo-url-table input {
        min-width: 80px;
        padding: 0.25rem;
    }

    .seo-url-table th,
    .seo-url-table td {
        padding: 0.5rem 0.25rem;
    }
}

/* Notification styles */
.alert {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.alert .close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.7;
}

.alert .close:hover {
    opacity: 1;
}

/* Image upload area */
.image-upload-area {
    transition: all 0.3s ease;
}

.image-upload-area:hover {
    border-color: #6b7280;
}

.image-upload-area.has-image {
    border-style: solid;
    border-color: #d1d5db;
}

/* Form sections */
.form-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

.form-section:first-child {
    border-top: none;
    padding-top: 0;
    margin-top: 0;
}

/* Store checkboxes */
.store-checkbox {
    transition: all 0.2s ease;
}

.store-checkbox:hover {
    background-color: #f9fafb;
}

/* SEO URL generation button */
.btn-generate-seo:hover {
    background-color: #eff6ff;
    border-radius: 0.25rem;
}

/* Tooltip styles */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.25rem;
}

[data-tooltip]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
    z-index: 1000;
}
</style>

<!-- JavaScript Integration -->
<script>
// Configuration for manufacturer form
window.manufacturerFormConfig = {
    urls: {
        action: '{{ action }}',
        cancel: '{{ cancel }}',
        imageManager: '{{ image_manager }}',
        checkSeoUrl: '{{ check_seo_url }}',
        placeholder: '{{ placeholder }}'
    },
    data: {
        manufacturer_id: {{ manufacturer_id }},
        name: '{{ name }}',
        image: '{{ image }}',
        sort_order: {{ sort_order }}
    },
    validation: {
        name: { required: true, minLength: 1, maxLength: 64 },
        sort_order: { required: false, type: 'number', min: 0 }
    },
    user_token: '{{ user_token }}',
    text: {
        confirmCancel: 'Имате незапазени промени. Сигурни ли сте, че искате да излезете?',
        confirmDelete: 'Сигурни ли сте, че искате да изтриете този производител?',
        nameRequired: 'Името на производителя е задължително!',
        nameLength: 'Името трябва да бъде между 1 и 64 символа!',
        sortOrderNumber: 'Подредбата трябва да бъде число!',
        sortOrderMin: 'Подредбата не може да бъде отрицателна!',
        seoUrlInvalid: 'SEO URL може да съдържа само букви, цифри, тире и долни черти!',
        seoUrlExists: 'Този SEO URL вече се използва!',
        imageTooBig: 'Изображението е твърде голямо! Максимален размер: 2MB',
        imageInvalidFormat: 'Неподдържан формат на изображението!',
        saveSuccess: 'Производителят е запазен успешно!',
        saveError: 'Възникна грешка при запазването!',
        deleteSuccess: 'Производителят е изтрит успешно!',
        deleteError: 'Възникна грешка при изтриването!',
        loading: 'Запазване...',
        enterNameFirst: 'Моля, въведете име на производителя първо!'
    }
};

// Initialize manufacturer form when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.ManufacturerForm) {
        window.ManufacturerForm.init();
    }

    // Initialize tooltips if available
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[title]').tooltip();
    }

    // Add form change detection
    let formChanged = false;
    const form = document.getElementById('manufacturer-form');
    if (form) {
        form.addEventListener('input', function() {
            formChanged = true;
        });

        form.addEventListener('change', function() {
            formChanged = true;
        });
    }

    // Warn before leaving if form has changes
    window.addEventListener('beforeunload', function(e) {
        if (formChanged) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
});

// Global functions for backward compatibility
function selectImage(image) {
    if (window.ManufacturerForm) {
        window.ManufacturerForm.setImage(image);
    }
}

function generateSeoUrl(storeId, languageId) {
    if (window.ManufacturerForm) {
        window.ManufacturerForm.generateSeoUrl(storeId, languageId);
    }
}

function validateForm() {
    if (window.ManufacturerForm) {
        return window.ManufacturerForm.validateForm();
    }
    return true;
}
</script>
