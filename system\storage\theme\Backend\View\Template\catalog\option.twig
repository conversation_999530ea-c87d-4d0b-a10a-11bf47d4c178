<!-- Option List Header -->
<div class="option-listing-page bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
                <p class="text-sm text-gray-600 mt-1">Управление на опциите на продуктите</p>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            <button id="delete-selected" type="button" disabled class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                <i class="ri-delete-bin-line mr-2"></i>
                Изтрий избраните
            </button>
            <a href="{{ add_url }}" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <i class="ri-add-line mr-2"></i>
                Добави опция
            </a>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <div class="max-w-7xl">

        <!-- Filter Section -->
        <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
            <form id="filter-form" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="relative">
                    <label for="filter-name" class="block text-sm font-medium text-gray-700 mb-1">Търсене по име</label>
                    <input type="text" id="filter-name" name="filter_name" value="{{ filter_name }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                           placeholder="Въведете име на опция..." autocomplete="off">
                    <!-- Autocomplete dropdown ще се добави динамично от JavaScript -->
                </div>

                <div>
                    <label for="filter-type" class="block text-sm font-medium text-gray-700 mb-1">Тип опция</label>
                    <select id="filter-type" name="filter_type"
                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                        <option value="">Всички типове</option>
                        {% for type_key, type_name in option_types %}
                        <option value="{{ type_key }}" {% if type_key == filter_type %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="flex items-end space-x-2">
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                        <i class="ri-search-line mr-2"></i>
                        Търси
                    </button>
                    <a href="{{ clear_filter_url }}" id="clear-filter" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors">
                        <i class="ri-close-line mr-2"></i>
                        Изчисти
                    </a>
                </div>
            </form>
        </div>

        <!-- Options Table -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer sort-header" data-sort="od.name">
                                <div class="flex items-center">
                                    Име
                                    <i class="ri-arrow-up-down-line ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer sort-header" data-sort="o.type">
                                <div class="flex items-center">
                                    Тип
                                    <i class="ri-arrow-up-down-line ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Стойности
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer sort-header" data-sort="o.sort_order">
                                <div class="flex items-center">
                                    Подредба
                                    <i class="ri-arrow-up-down-line ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% if options %}
                            {% for option in options %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="item-checkbox rounded border-gray-300 text-primary focus:ring-primary" value="{{ option.option_id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ option.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if option.type == 'select' %}bg-blue-100 text-blue-800
                                        {% elseif option.type == 'radio' %}bg-green-100 text-green-800
                                        {% elseif option.type == 'checkbox' %}bg-purple-100 text-purple-800
                                        {% elseif option.type == 'text' %}bg-gray-100 text-gray-800
                                        {% elseif option.type == 'textarea' %}bg-yellow-100 text-yellow-800
                                        {% elseif option.type == 'file' %}bg-red-100 text-red-800
                                        {% elseif option.type == 'date' %}bg-indigo-100 text-indigo-800
                                        {% elseif option.type == 'time' %}bg-pink-100 text-pink-800
                                        {% elseif option.type == 'datetime' %}bg-orange-100 text-orange-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ option.type_text }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if option.value_count > 0 %}
                                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">
                                            {{ option.value_count }} стойности
                                        </span>
                                    {% else %}
                                        <span class="text-gray-400">—</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ option.sort_order }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ option.edit_url }}" class="edit-btn text-primary hover:text-primary/80 transition-colors" title="Редактирай" data-edit-url="{{ option.edit_url }}">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <button type="button" class="delete-btn text-red-600 hover:text-red-800 transition-colors"
                                                data-option-id="{{ option.option_id }}"
                                                data-option-name="{{ option.name }}"
                                                title="Изтрий">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <div class="text-gray-500">
                                        <i class="ri-inbox-line text-4xl mb-4"></i>
                                        <p class="text-lg font-medium">Няма намерени опции</p>
                                        <p class="text-sm">Добавете първата опция или променете филтрите</p>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>


            <!-- Pagination -->
            {% if pagination_html %}
                {{ pagination_html|raw }}
            {% endif %}
        </div>
    </div>
</main>
