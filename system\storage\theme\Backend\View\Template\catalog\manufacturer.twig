<!-- Manufacturer List Header -->
<div class="manufacturer-listing-page bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Производители</h1>
                <p class="text-sm text-gray-600 mt-1">Управление на производителите на продуктите</p>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            <button id="button-delete" type="button" disabled class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                <i class="ri-delete-bin-line mr-2"></i>
                Изтрий избраните
            </button>
            <a href="{{ add }}" id="button-add" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <i class="ri-add-line mr-2"></i>
                Добави производител
            </a>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50">
    <div class="p-6">
        
        <!-- Filter Section -->
        <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
            <form id="filter-form" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="relative">
                    <label for="input-name" class="block text-sm font-medium text-gray-700 mb-1">Търсене по име</label>
                    <input type="text" id="input-name" name="filter_name" value="{{ filter_name }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                           placeholder="Въведете име на производител..." autocomplete="off">
                    <!-- Autocomplete dropdown ще се добави динамично от JavaScript -->
                </div>
                
                <div>
                    <label for="input-sort-order" class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                    <input type="number" id="input-sort-order" name="filter_sort_order" value="{{ filter_sort_order }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                           placeholder="Номер на подредба">
                </div>
                
                <div class="flex items-end">
                    <button type="button" id="filter-button" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors mr-2">
                        <i class="ri-search-line mr-1"></i>
                        Търси
                    </button>
                    <button type="button" id="clear-button" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-close-line mr-1"></i>
                        Изчисти
                    </button>
                </div>
            </form>
        </div>

        <!-- Manufacturers Table -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer sortable" data-sort="name" data-order="{{ order }}">
                                <div class="flex items-center">
                                    Име
                                    <i class="ri-arrow-up-down-line ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Изображение
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer sortable" data-sort="sort_order" data-order="{{ order }}">
                                <div class="flex items-center">
                                    Подредба
                                    <i class="ri-arrow-up-down-line ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% if manufacturers %}
                            {% for manufacturer in manufacturers %}
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" name="selected[]" value="{{ manufacturer.manufacturer_id }}" class="rounded border-gray-300 text-primary focus:ring-primary">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ manufacturer.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if manufacturer.image %}
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded object-cover" src="{{ manufacturer.image }}" alt="{{ manufacturer.name }}" loading="lazy">
                                        </div>
                                    {% else %}
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
                                            <i class="ri-image-line text-gray-400"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ manufacturer.sort_order }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ manufacturer.edit }}" class="btn-edit text-primary hover:text-primary/80 transition-colors" title="Редактирай">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <button type="button" class="btn-delete text-red-600 hover:text-red-800 transition-colors" title="Изтрий" data-id="{{ manufacturer.manufacturer_id }}">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center">
                                        <i class="ri-inbox-line text-4xl text-gray-300 mb-4"></i>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">Няма намерени производители</h3>
                                        <p class="text-gray-500 mb-4">Започнете като добавите първия производител</p>
                                        <a href="{{ add }}" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                                            <i class="ri-add-line mr-2"></i>
                                            Добави производител
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if manufacturers %}
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
                    <!-- Mobile pagination -->
                    {% if pagination %}
                        {{ pagination }}
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {{ results }}
                        </p>
                    </div>
                    <div>
                        {% if pagination %}
                            {{ pagination }}
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</main>

<!-- Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
        <span class="text-gray-700">Зареждане...</span>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<!-- Custom Styles -->
<style>
/* Autocomplete dropdown styles */
.autocomplete-dropdown {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
}

.autocomplete-option {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
}

.autocomplete-option:hover {
    background-color: #f9fafb;
}

.autocomplete-option:last-child {
    border-bottom: none;
}

.manufacturer-info {
    display: flex;
    align-items: center;
}

.manufacturer-thumb {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: 0.25rem;
    margin-right: 0.75rem;
}

.manufacturer-name {
    font-weight: 500;
}

/* Drag over styles */
.drag-over {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* Loading states */
.btn-loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive table */
@media (max-width: 768px) {
    .overflow-x-auto table {
        font-size: 0.875rem;
    }

    .overflow-x-auto th,
    .overflow-x-auto td {
        padding: 0.5rem;
    }
}

/* Sort indicators */
.sortable:hover {
    background-color: #f9fafb;
}

.sortable.active {
    background-color: #eff6ff;
}

.sortable.active i {
    color: #3b82f6;
}

/* Selection styles */
.table-row-selected {
    background-color: #eff6ff;
}

/* Notification styles */
.alert {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.alert .close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.7;
}

.alert .close:hover {
    opacity: 1;
}
</style>

<!-- JavaScript Integration -->
<script>
// Configuration for manufacturer listing
window.manufacturerConfig = {
    urls: {
        list: '{{ url_link }}',
        add: '{{ add }}',
        edit: '{{ edit }}',
        delete: '{{ delete }}',
        autocomplete: '{{ autocomplete }}',
        ajaxSearch: '{{ ajax_search }}'
    },
    filters: {
        name: '{{ filter_name }}',
        sort_order: '{{ filter_sort_order }}',
        sort: '{{ sort }}',
        order: '{{ order }}',
        page: {{ page }}
    },
    pagination: {
        total: {{ manufacturer_total }},
        limit: {{ limit }},
        page: {{ page }}
    },
    user_token: '{{ user_token }}',
    text: {
        confirmDelete: 'Сигурни ли сте, че искате да изтриете този производител?',
        confirmBulkDelete: 'Сигурни ли сте, че искате да изтриете избраните производители?',
        noSelection: 'Моля, изберете производители за изтриване!',
        deleteSuccess: 'Производителят е изтрит успешно!',
        deleteError: 'Възникна грешка при изтриването!',
        loading: 'Зареждане...',
        noResults: 'Няма намерени резултати'
    }
};

// Initialize manufacturer listing when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.ManufacturerListing) {
        window.ManufacturerListing.init();
    }

    // Initialize tooltips if available
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[title]').tooltip();
    }
});

// Global functions for backward compatibility
function confirmDelete(manufacturerId) {
    if (window.ManufacturerListing) {
        window.ManufacturerListing.confirmDelete(manufacturerId);
    }
}

function filterManufacturers() {
    if (window.ManufacturerListing) {
        window.ManufacturerListing.applyFilters();
    }
}

function clearFilters() {
    document.getElementById('input-name').value = '';
    document.getElementById('input-sort-order').value = '';
    if (window.ManufacturerListing) {
        window.ManufacturerListing.applyFilters();
    }
}
</script>
