/**
 * Manufacturer Form JavaScript Module
 * Управление на формата за производители
 */

(function() {
    'use strict';

    const ManufacturerForm = {
        
        // Конфигурация
        config: {
            maxImageSize: 2 * 1024 * 1024, // 2MB
            allowedImageTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
            validationRules: {
                name: { required: true, minLength: 1, maxLength: 64 },
                sort_order: { required: false, type: 'number', min: 0 }
            }
        },

        // Инициализация
        init: function() {
            this.bindEvents();
            this.initializeValidation();
            this.initializeImageManager();
            this.initializeSeoUrlGeneration();
            this.initializeMultiLanguage();
            this.initializeFormSubmission();
        },

        // Свързване на събития
        bindEvents: function() {
            // Запазване на формата
            const saveButton = document.getElementById('button-save');
            if (saveButton) {
                saveButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.submitForm();
                });
            }

            // Отказ/връщане
            const cancelButton = document.getElementById('button-cancel');
            if (cancelButton) {
                cancelButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.confirmCancel();
                });
            }

            // Валидация в реално време
            this.bindRealTimeValidation();

            // Генериране на SEO URL
            this.bindSeoUrlGeneration();

            // Image manager
            this.bindImageManager();
        },

        // Свързване на валидация в реално време
        bindRealTimeValidation: function() {
            // Валидация на името
            const nameInput = document.getElementById('input-name');
            if (nameInput) {
                nameInput.addEventListener('blur', () => {
                    this.validateField('name', nameInput.value);
                });

                nameInput.addEventListener('input', () => {
                    this.clearFieldError('name');
                });
            }

            // Валидация на sort_order
            const sortOrderInput = document.getElementById('input-sort-order');
            if (sortOrderInput) {
                sortOrderInput.addEventListener('blur', () => {
                    this.validateField('sort_order', sortOrderInput.value);
                });

                sortOrderInput.addEventListener('input', () => {
                    this.clearFieldError('sort_order');
                });
            }

            // Валидация на SEO URL полетата
            document.querySelectorAll('input[name*="manufacturer_seo_url"]').forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateSeoUrl(input);
                });

                input.addEventListener('input', () => {
                    this.clearSeoUrlError(input);
                });
            });
        },

        // Свързване на генерирането на SEO URL
        bindSeoUrlGeneration: function() {
            const nameInput = document.getElementById('input-name');
            if (nameInput) {
                nameInput.addEventListener('input', () => {
                    this.generateSeoUrls(nameInput.value);
                });
            }

            // Бутони за генериране на SEO URL
            document.querySelectorAll('.btn-generate-seo').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const storeId = button.getAttribute('data-store-id');
                    const languageId = button.getAttribute('data-language-id');
                    this.generateSeoUrl(storeId, languageId);
                });
            });
        },

        // Свързване на image manager
        bindImageManager: function() {
            const imageButton = document.getElementById('button-image');
            if (imageButton) {
                imageButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.openImageManager();
                });
            }

            const clearImageButton = document.getElementById('button-clear-image');
            if (clearImageButton) {
                clearImageButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.clearImage();
                });
            }
        },

        // Инициализация на валидацията
        initializeValidation: function() {
            // Добавяне на CSS класове за валидация
            this.addValidationClasses();
        },

        // Добавяне на CSS класове за валидация
        addValidationClasses: function() {
            Object.keys(this.config.validationRules).forEach(fieldName => {
                const field = document.getElementById(`input-${fieldName}`);
                if (field && this.config.validationRules[fieldName].required) {
                    field.classList.add('required');
                }
            });
        },

        // Валидация на поле
        validateField: function(fieldName, value) {
            const rules = this.config.validationRules[fieldName];
            if (!rules) return true;

            const errors = [];

            // Проверка за задължително поле
            if (rules.required && (!value || value.trim() === '')) {
                errors.push('Това поле е задължително!');
            }

            // Проверка за минимална дължина
            if (rules.minLength && value.length < rules.minLength) {
                errors.push(`Минимална дължина: ${rules.minLength} символа!`);
            }

            // Проверка за максимална дължина
            if (rules.maxLength && value.length > rules.maxLength) {
                errors.push(`Максимална дължина: ${rules.maxLength} символа!`);
            }

            // Проверка за тип число
            if (rules.type === 'number' && value && isNaN(value)) {
                errors.push('Трябва да бъде число!');
            }

            // Проверка за минимална стойност
            if (rules.min !== undefined && value && parseFloat(value) < rules.min) {
                errors.push(`Минимална стойност: ${rules.min}!`);
            }

            if (errors.length > 0) {
                this.showFieldError(fieldName, errors[0]);
                return false;
            } else {
                this.clearFieldError(fieldName);
                return true;
            }
        },

        // Показване на грешка за поле
        showFieldError: function(fieldName, message) {
            const field = document.getElementById(`input-${fieldName}`);
            if (!field) return;

            field.classList.add('is-invalid');

            let errorDiv = field.parentNode.querySelector('.invalid-feedback');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                field.parentNode.appendChild(errorDiv);
            }

            errorDiv.textContent = message;
        },

        // Изчистване на грешка за поле
        clearFieldError: function(fieldName) {
            const field = document.getElementById(`input-${fieldName}`);
            if (!field) return;

            field.classList.remove('is-invalid');

            const errorDiv = field.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        },

        // Валидация на SEO URL
        validateSeoUrl: function(input) {
            const value = input.value.trim();
            
            if (value === '') {
                this.clearSeoUrlError(input);
                return true;
            }

            // Проверка за валиден формат
            if (!/^[a-zA-Z0-9\-_]+$/.test(value)) {
                this.showSeoUrlError(input, 'SEO URL може да съдържа само букви, цифри, тире и долни черти!');
                return false;
            }

            // Проверка за уникалност (AJAX заявка)
            this.checkSeoUrlUniqueness(input, value);
            
            return true;
        },

        // Проверка за уникалност на SEO URL
        checkSeoUrlUniqueness: function(input, value) {
            const manufacturerId = document.getElementById('manufacturer-id')?.value || 0;
            const url = `index.php?route=catalog/manufacturer/checkSeoUrl&keyword=${encodeURIComponent(value)}&manufacturer_id=${manufacturerId}`;
            
            this.makeRequest(url, 'GET')
                .then(data => {
                    if (data.exists) {
                        this.showSeoUrlError(input, 'Този SEO URL вече се използва!');
                    } else {
                        this.clearSeoUrlError(input);
                    }
                })
                .catch(error => {
                    console.error('SEO URL check error:', error);
                });
        },

        // Показване на грешка за SEO URL
        showSeoUrlError: function(input, message) {
            input.classList.add('is-invalid');

            let errorDiv = input.parentNode.querySelector('.invalid-feedback');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                input.parentNode.appendChild(errorDiv);
            }

            errorDiv.textContent = message;
        },

        // Изчистване на грешка за SEO URL
        clearSeoUrlError: function(input) {
            input.classList.remove('is-invalid');

            const errorDiv = input.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        },

        // Генериране на SEO URLs за всички езици и магазини
        generateSeoUrls: function(name) {
            if (!name || name.trim() === '') return;

            const seoUrl = this.generateSeoUrlFromName(name);
            
            document.querySelectorAll('input[name*="manufacturer_seo_url"]').forEach(input => {
                if (input.value.trim() === '') {
                    input.value = seoUrl;
                }
            });
        },

        // Генериране на SEO URL за конкретен магазин и език
        generateSeoUrl: function(storeId, languageId) {
            const nameInput = document.getElementById('input-name');
            if (!nameInput || !nameInput.value.trim()) {
                alert('Моля, въведете име на производителя първо!');
                return;
            }

            const seoUrl = this.generateSeoUrlFromName(nameInput.value);
            const input = document.querySelector(`input[name="manufacturer_seo_url[${storeId}][${languageId}]"]`);
            
            if (input) {
                input.value = seoUrl;
                this.validateSeoUrl(input);
            }
        },

        // Генериране на SEO URL от име
        generateSeoUrlFromName: function(name) {
            return name
                .toLowerCase()
                .trim()
                .replace(/[^\w\s\-]/g, '') // Премахване на специални символи
                .replace(/\s+/g, '-') // Заместване на интервали с тирета
                .replace(/\-+/g, '-') // Премахване на множествени тирета
                .replace(/^\-|\-$/g, ''); // Премахване на тирета в началото и края
        },

        // Инициализация на image manager
        initializeImageManager: function() {
            // Drag & drop функционалност
            this.initializeDragAndDrop();

            // Preview на изображението
            this.initializeImagePreview();
        },

        // Инициализация на drag & drop
        initializeDragAndDrop: function() {
            const imageContainer = document.getElementById('image-container');
            if (!imageContainer) return;

            imageContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                imageContainer.classList.add('drag-over');
            });

            imageContainer.addEventListener('dragleave', (e) => {
                e.preventDefault();
                imageContainer.classList.remove('drag-over');
            });

            imageContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                imageContainer.classList.remove('drag-over');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleImageUpload(files[0]);
                }
            });
        },

        // Инициализация на image preview
        initializeImagePreview: function() {
            const imageInput = document.getElementById('input-image');
            if (imageInput) {
                imageInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.handleImageUpload(e.target.files[0]);
                    }
                });
            }
        },

        // Обработка на качване на изображение
        handleImageUpload: function(file) {
            // Валидация на файла
            if (!this.validateImageFile(file)) {
                return;
            }

            // Показване на preview
            this.showImagePreview(file);

            // Качване на файла (ако е необходимо)
            // this.uploadImage(file);
        },

        // Валидация на image файл
        validateImageFile: function(file) {
            // Проверка на типа
            if (!this.config.allowedImageTypes.includes(file.type)) {
                alert('Неподдържан формат на изображението! Разрешени формати: JPG, PNG, GIF, WebP');
                return false;
            }

            // Проверка на размера
            if (file.size > this.config.maxImageSize) {
                alert('Изображението е твърде голямо! Максимален размер: 2MB');
                return false;
            }

            return true;
        },

        // Показване на preview на изображението
        showImagePreview: function(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imagePreview = document.getElementById('image-preview');
                if (imagePreview) {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = 'block';
                }
            };
            reader.readAsDataURL(file);
        },

        // Отваряне на image manager
        openImageManager: function() {
            // Тук ще се интегрира с image manager модула
            if (window.ImageManager) {
                window.ImageManager.open({
                    callback: (imagePath) => {
                        this.setImage(imagePath);
                    }
                });
            } else {
                // Fallback - отваряне в нов прозорец
                const imageManagerUrl = document.getElementById('button-image').getAttribute('data-href');
                if (imageManagerUrl) {
                    window.open(imageManagerUrl, 'imagemanager', 'width=800,height=600');
                }
            }
        },

        // Задаване на изображение
        setImage: function(imagePath) {
            const imageInput = document.getElementById('input-image');
            const imagePreview = document.getElementById('image-preview');

            if (imageInput) {
                imageInput.value = imagePath;
            }

            if (imagePreview) {
                imagePreview.src = imagePath;
                imagePreview.style.display = 'block';
            }
        },

        // Изчистване на изображението
        clearImage: function() {
            const imageInput = document.getElementById('input-image');
            const imagePreview = document.getElementById('image-preview');

            if (imageInput) {
                imageInput.value = '';
            }

            if (imagePreview) {
                imagePreview.src = '';
                imagePreview.style.display = 'none';
            }
        },

        // Инициализация на мултиезичност
        initializeMultiLanguage: function() {
            // Табове за езици (ако има такива)
            this.initializeLanguageTabs();

            // Копиране на данни между езици
            this.initializeLanguageCopy();
        },

        // Инициализация на табове за езици
        initializeLanguageTabs: function() {
            document.querySelectorAll('.language-tab').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.switchLanguageTab(tab.getAttribute('data-language-id'));
                });
            });
        },

        // Превключване на таб за език
        switchLanguageTab: function(languageId) {
            // Скриване на всички табове
            document.querySelectorAll('.language-content').forEach(content => {
                content.style.display = 'none';
            });

            // Показване на избрания таб
            const targetContent = document.getElementById(`language-${languageId}`);
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // Актуализиране на активния таб
            document.querySelectorAll('.language-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            const activeTab = document.querySelector(`[data-language-id="${languageId}"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
        },

        // Инициализация на копиране между езици
        initializeLanguageCopy: function() {
            document.querySelectorAll('.btn-copy-language').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const fromLanguage = button.getAttribute('data-from-language');
                    const toLanguage = button.getAttribute('data-to-language');
                    this.copyLanguageData(fromLanguage, toLanguage);
                });
            });
        },

        // Копиране на данни между езици
        copyLanguageData: function(fromLanguage, toLanguage) {
            const fromFields = document.querySelectorAll(`[data-language="${fromLanguage}"]`);

            fromFields.forEach(fromField => {
                const fieldName = fromField.getAttribute('name').replace(`[${fromLanguage}]`, `[${toLanguage}]`);
                const toField = document.querySelector(`[name="${fieldName}"]`);

                if (toField) {
                    toField.value = fromField.value;
                }
            });

            alert(`Данните са копирани от език ${fromLanguage} към език ${toLanguage}!`);
        },

        // Инициализация на изпращането на формата
        initializeFormSubmission: function() {
            const form = document.getElementById('manufacturer-form');
            if (form) {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.submitForm();
                });
            }
        },

        // Изпращане на формата
        submitForm: function() {
            // Валидация на цялата форма
            if (!this.validateForm()) {
                this.showNotification('Моля, поправете грешките във формата!', 'error');
                return;
            }

            // Показване на loading индикатор
            this.showLoadingIndicator();

            // Подготвяне на данните
            const formData = this.prepareFormData();

            // Изпращане на заявката
            const form = document.getElementById('manufacturer-form');
            const actionUrl = form.getAttribute('action');

            this.makeRequest(actionUrl, 'POST', formData)
                .then(data => {
                    this.hideLoadingIndicator();

                    if (data.success) {
                        this.showNotification(data.success, 'success');

                        // Пренасочване след успешно запазване
                        if (data.redirect) {
                            setTimeout(() => {
                                window.location = data.redirect;
                            }, 1000);
                        }
                    } else if (data.error) {
                        this.showNotification(data.error, 'error');

                        // Показване на грешки за полета
                        if (data.errors) {
                            this.showFormErrors(data.errors);
                        }
                    }
                })
                .catch(error => {
                    this.hideLoadingIndicator();
                    this.showNotification('Възникна грешка при запазването!', 'error');
                    console.error('Form submission error:', error);
                });
        },

        // Валидация на цялата форма
        validateForm: function() {
            let isValid = true;

            // Валидация на всички полета
            Object.keys(this.config.validationRules).forEach(fieldName => {
                const field = document.getElementById(`input-${fieldName}`);
                if (field) {
                    if (!this.validateField(fieldName, field.value)) {
                        isValid = false;
                    }
                }
            });

            // Валидация на SEO URL полетата
            document.querySelectorAll('input[name*="manufacturer_seo_url"]').forEach(input => {
                if (!this.validateSeoUrl(input)) {
                    isValid = false;
                }
            });

            return isValid;
        },

        // Подготвяне на данните от формата
        prepareFormData: function() {
            const form = document.getElementById('manufacturer-form');
            return new FormData(form);
        },

        // Показване на грешки за полета
        showFormErrors: function(errors) {
            Object.keys(errors).forEach(fieldName => {
                if (fieldName === 'keyword') {
                    // Специална обработка за SEO URL грешки
                    Object.keys(errors[fieldName]).forEach(storeId => {
                        Object.keys(errors[fieldName][storeId]).forEach(languageId => {
                            const input = document.querySelector(`input[name="manufacturer_seo_url[${storeId}][${languageId}]"]`);
                            if (input) {
                                this.showSeoUrlError(input, errors[fieldName][storeId][languageId]);
                            }
                        });
                    });
                } else {
                    this.showFieldError(fieldName, errors[fieldName]);
                }
            });
        },

        // Показване на loading индикатор
        showLoadingIndicator: function() {
            const saveButton = document.getElementById('button-save');
            if (saveButton) {
                saveButton.disabled = true;
                saveButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Запазване...';
            }
        },

        // Скриване на loading индикатор
        hideLoadingIndicator: function() {
            const saveButton = document.getElementById('button-save');
            if (saveButton) {
                saveButton.disabled = false;
                saveButton.innerHTML = '<i class="fa fa-save"></i> Запази';
            }
        },

        // Потвърждение за отказ
        confirmCancel: function() {
            if (this.hasUnsavedChanges()) {
                if (confirm('Имате незапазени промени. Сигурни ли сте, че искате да излезете?')) {
                    const cancelUrl = document.getElementById('button-cancel').getAttribute('href');
                    window.location = cancelUrl;
                }
            } else {
                const cancelUrl = document.getElementById('button-cancel').getAttribute('href');
                window.location = cancelUrl;
            }
        },

        // Проверка за незапазени промени
        hasUnsavedChanges: function() {
            // Тук можем да сравним текущите стойности с първоначалните
            // За простота, ще проверим дали има стойности в полетата
            const nameInput = document.getElementById('input-name');
            return nameInput && nameInput.value.trim() !== '';
        },

        // Показване на нотификация
        showNotification: function(message, type = 'info') {
            // Създаване на нотификация
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible`;
            notification.innerHTML = `
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                ${message}
            `;

            // Добавяне в контейнера за нотификации
            let container = document.getElementById('notification-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'notification-container';
                container.style.position = 'fixed';
                container.style.top = '20px';
                container.style.right = '20px';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
            }

            container.appendChild(notification);

            // Автоматично премахване след 5 секунди
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        // Помощна функция за HTTP заявки
        makeRequest: function(url, method = 'GET', data = null) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(method, url);

                if (method === 'POST' && !(data instanceof FormData)) {
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                }

                xhr.onload = () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                };

                xhr.onerror = () => {
                    reject(new Error('Network error'));
                };

                xhr.send(data);
            });
        }
    };

    // Инициализация при зареждане на DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            ManufacturerForm.init();
        });
    } else {
        ManufacturerForm.init();
    }

    // Експортиране за глобален достъп
    window.ManufacturerForm = ManufacturerForm;

})();
