/**
 * Attribute Form Module
 * Управление на формата за атрибути - интегриран в BackendModule
 */
(function() {
    'use strict';

    // Разширяване на BackendModule с функционалност за атрибути
    Object.assign(BackendModule, {

        // Конфигурация за атрибути
        attributeForm: {
            config: {
                saveUrl: 'index.php?route=catalog/attribute/save',
                autocompleteUrl: 'index.php?route=catalog/attribute/autocomplete'
            },

            /**
             * Инициализация на формата за атрибути
             */
            init: function() {
                console.log('Initializing attribute form...');
                this.bindEvents();
                this.initializeComponents();
            },

            /**
             * Свързване на събития
             */
            bindEvents: function() {
                this.bindFormEvents();
                this.bindValidationEvents();
                this.bindLanguageTabEvents();
            },

            /**
             * Свързване на събития за формата
             */
            bindFormEvents: function() {
                const form = document.getElementById('attribute-form');
                const self = this; // Запазване на правилната референция

                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        self.saveAttribute();
                    });
                }

                // Автоматично запазване при промяна на полетата
                const autoSaveFields = form?.querySelectorAll('input[name*="attribute_description"], select[name="attribute_group_id"], input[name="sort_order"]');
                autoSaveFields?.forEach(field => {
                    field.addEventListener('blur', () => {
                        self.validateSpecificField(field);
                    });
                });
            },

            /**
             * Свързване на събития за валидация
             */
            bindValidationEvents: function() {
                const nameInputs = document.querySelectorAll('input[name*="[name]"]');
                const self = this; // Запазване на правилната референция

                nameInputs.forEach(input => {
                    input.addEventListener('input', () => {
                        self.validateNameField(input);
                    });

                    input.addEventListener('blur', () => {
                        self.validateNameField(input);
                    });
                });

                const sortOrderInput = document.querySelector('input[name="sort_order"]');
                if (sortOrderInput) {
                    sortOrderInput.addEventListener('input', () => {
                        self.validateSortOrder(sortOrderInput);
                    });
                }

                // Автоматично попълване на sort_order при избор на група (само за нови атрибути)
                this.bindSortOrderAutoFill();
            },

            /**
             * Свързване на събития за езиковите табове
             */
            bindLanguageTabEvents: function() {
                const languageTabs = document.querySelectorAll('.language-tab');
                const self = this; // Запазване на правилната референция

                languageTabs.forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        self.switchLanguageTab(tab);
                    });
                });
            },

        /**
         * Инициализация на компоненти
         */
        initializeComponents: function() {
            this.initializeLanguageTabs();
            this.initializeTooltips();
            this.initializeDisabledFields();
            this.validateAllFields();
        },

        /**
         * Инициализация на disabled полета
         */
        initializeDisabledFields: function() {
            // Проверяваме дали сме в режим на редактиране
            const attributeIdField = document.querySelector('input[name="attribute_id"]');
            const isEditMode = attributeIdField && attributeIdField.value && attributeIdField.value !== '0';

            if (isEditMode) {
                console.log('Edit mode detected - attribute group field is disabled');

                // Добавяме tooltip за обяснение
                this.addGroupFieldTooltip();
            }
        },

        /**
         * Добавяне на tooltip за групата атрибути
         */
        addGroupFieldTooltip: function() {
            const groupSelect = document.querySelector('select[name="attribute_group_id"]');
            if (!groupSelect) return;

            // Добавяме tooltip атрибут
            groupSelect.setAttribute('title', 'Групата на атрибута не може да се променя след създаването му');

            // Можем да добавим и hover ефект
            groupSelect.addEventListener('mouseenter', function() {
                if (this.disabled) {
                    console.log('Group field is disabled - showing tooltip');
                }
            });
        },

        /**
         * Инициализация на езиковите табове
         */
        initializeLanguageTabs: function() {
            const firstTab = document.querySelector('.language-tab');
            if (firstTab) {
                this.switchLanguageTab(firstTab);
            }
        },

        /**
         * Инициализация на tooltips
         */
        initializeTooltips: function() {
            const tooltipElements = document.querySelectorAll('[data-tooltip]');
            
            tooltipElements.forEach(element => {
                element.addEventListener('mouseenter', (e) => {
                    this.showTooltip(e.target);
                });
                
                element.addEventListener('mouseleave', (e) => {
                    this.hideTooltip(e.target);
                });
            });
        },

        /**
         * Превключване на езиков таб
         */
        switchLanguageTab: function(clickedTab) {
            const languageId = clickedTab.dataset.languageId;
            
            // Деактивиране на всички табове
            document.querySelectorAll('.language-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Скриване на всички езикови панели
            document.querySelectorAll('.language-panel').forEach(panel => {
                panel.classList.add('hidden');
            });
            
            // Активиране на избрания таб
            clickedTab.classList.add('active');
            
            // Показване на съответния панел
            const targetPanel = document.getElementById(`language-panel-${languageId}`);
            if (targetPanel) {
                targetPanel.classList.remove('hidden');
            }
        },

        /**
         * Валидация на поле за име
         */
        validateNameField: function(input) {
            const value = input.value.trim();
            
            // Премахване на предишни грешки
            this.clearFieldError(input);
            
            if (value.length === 0) {
                this.showFieldError(input, 'Името на атрибута е задължително');
                return false;
            }
            
            if (value.length > 64) {
                this.showFieldError(input, 'Името не може да бъде по-дълго от 64 символа');
                return false;
            }
            
            return true;
        },

        /**
         * Валидация на полето за подредба
         */
        validateSortOrder: function(input) {
            const value = input.value.trim();
            
            this.clearFieldError(input);
            
            if (value !== '' && !this.isNumeric(value)) {
                this.showFieldError(input, 'Подредбата трябва да бъде число');
                return false;
            }
            
            return true;
        },

        /**
         * Валидация на всички полета
         */
        validateAllFields: function() {
            let isValid = true;
            
            // Валидация на имената
            const nameInputs = document.querySelectorAll('input[name*="[name]"]');
            nameInputs.forEach(input => {
                if (!this.validateNameField(input)) {
                    isValid = false;
                }
            });
            
            // Валидация на подредбата
            const sortOrderInput = document.querySelector('input[name="sort_order"]');
            if (sortOrderInput && !this.validateSortOrder(sortOrderInput)) {
                isValid = false;
            }
            
            // Проверка дали има поне едно име
            const hasValidName = Array.from(nameInputs).some(input => input.value.trim().length > 0);
            if (!hasValidName) {
                // this.showGeneralError('Трябва да въведете име на атрибута поне за един език');
                isValid = false;
            }
            
            return isValid;
        },

        /**
         * Валидация на конкретно поле според типа му
         */
        validateSpecificField: function(field) {
            const fieldName = field.name;

            if (fieldName.includes('attribute_description') && fieldName.includes('[name]')) {
                // Поле за име на атрибут
                return this.validateNameField(field);
            } else if (fieldName === 'sort_order') {
                // Поле за подредба
                return this.validateSortOrder(field);
            } else if (fieldName === 'attribute_group_id') {
                // Поле за група атрибути - основна валидация
                this.clearFieldError(field);
                if (!field.value || field.value === '0') {
                    this.showFieldError(field, 'Моля, изберете група атрибути');
                    return false;
                }
                return true;
            }

            // За други полета - само изчистваме грешките
            this.clearFieldError(field);
            return true;
        },

            /**
             * Запазване на атрибут
             */
            saveAttribute: function() {
                if (!this.validateAllFields()) {
                    return;
                }

                const form = document.getElementById('attribute-form');
                const formData = new FormData(form);
                formData.append('user_token', BackendModule.config.userToken);

                // Показване на loading индикатор
                this.showLoading();

                fetch(this.config.saveUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    this.hideLoading();

                    if (data.success) {
                        BackendModule.showAlert('success', data.success);

                        // Пренасочване след успешно запазване
                        if (data.redirect) {
                            setTimeout(() => {
                                window.location.href = data.redirect;
                            }, 1500);
                        }
                    } else if (data.errors) {
                        this.showValidationErrors(data.errors);
                    } else if (data.error) {
                        BackendModule.showAlert('error', data.error);
                    }
                })
                .catch(error => {
                    this.hideLoading();
                    console.error('Грешка при запазване:', error);
                    BackendModule.showAlert('error', 'Възникна грешка при запазване на атрибута');
                });
            },

            /**
             * Показване на грешка за поле
             */
            showFieldError: function(field, message) {
                // Добавяне на червена граница
                field.classList.add('border-red-500');

                // Премахване на съществуваща грешка
                this.clearFieldError(field, false);

                // Създаване на нов error element
                const errorElement = document.createElement('div');
                errorElement.className = 'error-message text-red-500 text-sm mt-1';
                errorElement.textContent = message;

                // Добавяне на грешката след полето
                field.parentNode.appendChild(errorElement);
            },

            /**
             * Изчистване на грешка за поле
             */
            clearFieldError: function(field, removeBorder = true) {
                if (removeBorder) {
                    field.classList.remove('border-red-500');
                }

                const errorElement = field.parentNode.querySelector('.error-message');
                if (errorElement) {
                    errorElement.remove();
                }
            },

            /**
             * Показване на валидационни грешки
             */
            showValidationErrors: function(errors) {
                // Изчистване на предишни грешки
                this.clearAllErrors();

                let hasFieldErrors = false;

                Object.keys(errors).forEach(fieldName => {
                    if (fieldName === 'name' && typeof errors[fieldName] === 'object') {
                        // Грешки за имена по езици
                        Object.keys(errors[fieldName]).forEach(languageId => {
                            const field = document.querySelector(`input[name="attribute_description[${languageId}][name]"]`);
                            if (field) {
                                this.showFieldError(field, errors[fieldName][languageId]);
                                hasFieldErrors = true;
                            }
                        });
                    } else if (fieldName === 'attribute_group_id' || fieldName === 'attribute_group') {
                        // Грешка за група атрибути
                        const field = document.querySelector('select[name="attribute_group_id"]');
                        if (field) {
                            this.showFieldError(field, errors[fieldName]);
                            hasFieldErrors = true;
                        }
                    } else if (fieldName === 'sort_order') {
                        // Грешка за подредба
                        const field = document.querySelector('input[name="sort_order"]');
                        if (field) {
                            this.showFieldError(field, errors[fieldName]);
                            hasFieldErrors = true;
                        }
                    } else {
                        // Други грешки - показваме като общи
                        BackendModule.showAlert('error', errors[fieldName]);
                    }
                });

                // Ако няма грешки за конкретни полета, показваме общо съобщение
                if (!hasFieldErrors && Object.keys(errors).length > 0) {
                    const errorMessages = Object.values(errors).join(', ');
                    BackendModule.showAlert('error', errorMessages);
                }
            },

        /**
         * Изчистване на всички грешки
         */
        clearAllErrors: function() {
            document.querySelectorAll('.border-red-500').forEach(field => {
                field.classList.remove('border-red-500');
            });
            
            document.querySelectorAll('.error-message').forEach(error => {
                error.remove();
            });
            
            this.hideGeneralError();
        },

            /**
             * Помощни методи
             */
            isNumeric: function(value) {
                return !isNaN(value) && !isNaN(parseFloat(value));
            },

            showLoading: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }
            },

            hideLoading: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                }
            },

            /**
             * Свързване на автоматично попълване на sort_order
             */
            bindSortOrderAutoFill: function() {
                const attributeGroupSelect = document.querySelector('select[name="attribute_group_id"]');
                const sortOrderInput = document.querySelector('input[name="sort_order"]');
                const attributeIdInput = document.querySelector('input[name="attribute_id"]');

                // Проверка дали е форма за добавяне на нов атрибут
                const isNewAttribute = !attributeIdInput || attributeIdInput.value === '0' || attributeIdInput.value === '';

                if (!attributeGroupSelect || !sortOrderInput || !isNewAttribute) {
                    return;
                }

                console.log('Setting up sort_order auto-fill for new attribute');

                const self = this; // Запазване на правилната референция
                let debounceTimer;

                attributeGroupSelect.addEventListener('change', () => {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        self.autoFillSortOrder(attributeGroupSelect, sortOrderInput);
                    }, 200);
                });
            },

            /**
             * Автоматично попълване на sort_order стойността
             */
            autoFillSortOrder: function(groupSelect, sortOrderInput) {
                const attributeGroupId = groupSelect.value;

                if (!attributeGroupId || attributeGroupId === '0' || attributeGroupId === '') {
                    console.log('No attribute group selected, clearing sort_order');
                    sortOrderInput.value = '';
                    return;
                }

                console.log('Auto-filling sort_order for group:', attributeGroupId);

                // Показване на loading индикатор
                this.showSortOrderLoading(sortOrderInput);

                // AJAX заявка за получаване на следващата стойност
                const params = new URLSearchParams();
                params.append('user_token', BackendModule.config.userToken);
                params.append('attribute_group_id', attributeGroupId);

                fetch(`index.php?route=catalog/attribute/getNextSortOrder&${params.toString()}`)
                    .then(response => response.json())
                    .then(data => {
                        this.hideSortOrderLoading(sortOrderInput);

                        if (data.success && data.next_sort_order) {
                            console.log('Setting sort_order to:', data.next_sort_order);
                            sortOrderInput.value = data.next_sort_order;

                            // Премахване на грешки ако има такива
                            this.clearFieldError(sortOrderInput);
                        } else if (data.error) {
                            console.warn('Error getting next sort_order:', data.error);
                            sortOrderInput.value = '';
                        } else {
                            console.warn('Unexpected response:', data);
                            sortOrderInput.value = '1'; // Fallback стойност
                        }
                    })
                    .catch(error => {
                        this.hideSortOrderLoading(sortOrderInput);
                        console.warn('AJAX error getting next sort_order:', error);
                        sortOrderInput.value = ''; // Оставяме полето празно при грешка
                    });
            },

            /**
             * Показване на loading индикатор в sort_order полето
             */
            showSortOrderLoading: function(input) {
                input.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23666\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M21 12a9 9 0 11-6.219-8.56\'/%3E%3C/svg%3E")';
                input.style.backgroundRepeat = 'no-repeat';
                input.style.backgroundPosition = 'right 8px center';
                input.style.backgroundSize = '16px 16px';
                input.style.paddingRight = '32px';
                input.disabled = true;
                input.placeholder = 'Зареждане...';
            },

            /**
             * Скриване на loading индикатор в sort_order полето
             */
            hideSortOrderLoading: function(input) {
                input.style.backgroundImage = '';
                input.style.backgroundRepeat = '';
                input.style.backgroundPosition = '';
                input.style.backgroundSize = '';
                input.style.paddingRight = '';
                input.disabled = false;
                input.placeholder = '0';
            }
        }
    });

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('attribute-form')) {
            BackendModule.attributeForm.init();
        }
    });

})();
