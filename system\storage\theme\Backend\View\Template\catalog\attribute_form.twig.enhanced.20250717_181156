<!-- Attribute Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
                <div class="w-8 h-8 flex items-center justify-center">
                    <i class="ri-arrow-left-line ri-lg"></i>
                </div>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            {% if attribute_id > 0 and delete_url %}
            <button type="button" id="delete-attribute" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors">
                <i class="ri-delete-bin-line mr-2"></i>
                Изтрий
            </button>
            {% endif %}
            <button type="submit" form="attribute-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <i class="ri-save-line mr-2"></i>
                Запази
            </button>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <form id="attribute-form" class="space-y-6">
        <input type="hidden" name="attribute_id" value="{{ attribute_id }}">
        
        <div class="max-w-4xl">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                
                <!-- Form Content -->
                <div class="p-6">
                    
                    <!-- Basic Information Section -->
                    <div class="space-y-6">
                        
                        <!-- Attribute Group -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Група атрибути <span class="text-red-500">*</span>
                                {% if attribute_id > 0 %}
                                <span class="inline-flex items-center ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                    <i class="ri-lock-line mr-1"></i>
                                    Заключено
                                </span>
                                {% endif %}
                            </label>
                            <select name="attribute_group_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm {% if attribute_id > 0 %}bg-gray-100 cursor-not-allowed{% endif %}"
                                    {% if attribute_id > 0 %}disabled{% endif %}>
                                <option value="">Изберете група атрибути</option>
                                {% for group in attribute_groups %}
                                <option value="{{ group.attribute_group_id }}" {% if group.attribute_group_id == attribute_group_id %}selected{% endif %}>
                                    {{ group.name }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if attribute_id > 0 %}
                                <!-- Hidden field to preserve value when disabled -->
                                <input type="hidden" name="attribute_group_id" value="{{ attribute_group_id }}">
                                <p class="text-xs text-yellow-600 mt-1 flex items-center">
                                    <i class="ri-information-line mr-1"></i>
                                    Групата на атрибута не може да се променя след създаването му
                                </p>
                            {% else %}
                                <p class="text-xs text-gray-500 mt-1">Групата определя организацията на атрибутите</p>
                            {% endif %}
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                            <input type="number" name="sort_order" value="{{ sort_order }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" 
                                   placeholder="0">
                            <p class="text-xs text-gray-500 mt-1">По-ниските числа се показват първи</p>
                        </div>

                        <!-- Language Tabs -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Имена на атрибута</h3>
                            
                            <!-- Language Tab Headers -->
                            <div class="border-b border-gray-200 mb-4">
                                <nav class="-mb-px flex space-x-8">
                                    {% for language in languages %}
                                    <button type="button" class="language-tab py-2 px-1 border-b-2 font-medium text-sm {% if loop.first %}border-primary text-primary{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}" 
                                            data-language-id="{{ language.language_id }}">
                                        <div class="flex items-center">
                                            <div class="w-6 h-4 flex items-center justify-center {{ language.css }} rounded text-xs font-medium text-white mr-2">
                                                {{ language.code|upper|slice(0, 2) }}
                                            </div>
                                            {{ language.name }}
                                        </div>
                                    </button>
                                    {% endfor %}
                                </nav>
                            </div>

                            <!-- Language Tab Content -->
                            {% for language in languages %}
                            <div id="language-panel-{{ language.language_id }}" class="language-panel {% if not loop.first %}hidden{% endif %}">
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Име на атрибута <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" name="attribute_description[{{ language.language_id }}][name]" 
                                               value="{{ attribute_description[language.language_id].name }}" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" 
                                               placeholder="Въведете име на атрибута за {{ language.name }}"
                                               maxlength="64">
                                        <p class="text-xs text-gray-500 mt-1">Максимум 64 символа</p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Help Text -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-information-line text-blue-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">Информация за атрибутите</h3>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>Атрибутите се използват за описание на характеристиките на продуктите</li>
                                            <li>Всеки атрибут трябва да принадлежи към група атрибути</li>
                                            <li>Името на атрибута трябва да бъде уникално в рамките на групата</li>
                                            <li>Подредбата определя реда на показване в продуктовите форми</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</main>

<script>
// Обработка на изтриване на атрибут
document.addEventListener('DOMContentLoaded', function() {
    const deleteBtn = document.getElementById('delete-attribute');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('Сигурни ли сте, че искате да изтриете този атрибут?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ delete_url }}';
                
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = 'user_token';
                tokenInput.value = '{{ user_token }}';
                
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'attribute_id';
                idInput.value = '{{ attribute_id }}';
                
                form.appendChild(tokenInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
});
</script>
