/**
 * Manufacturer Listing JavaScript Module
 * Управление на листването на производители
 */

(function() {
    'use strict';

    const ManufacturerListing = {
        
        // Конфигурация
        config: {
            searchDelay: 500,
            maxConcurrentRequests: 10,
            requestQueue: [],
            activeRequests: 0
        },

        // Инициализация
        init: function() {
            this.bindEvents();
            this.initializeFilters();
            this.initializeSearch();
            this.initializeBulkActions();
            this.initializeTooltips();
        },

        // Свързване на събития
        bindEvents: function() {
            // Търсене
            const searchInput = document.getElementById('input-name');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        this.performSearch(e.target.value);
                    }, this.config.searchDelay);
                });
            }

            // Филтър по sort_order
            const sortOrderInput = document.getElementById('input-sort-order');
            if (sortOrderInput) {
                sortOrderInput.addEventListener('change', () => {
                    this.applyFilters();
                });
            }

            // Бутони за действия
            this.bindActionButtons();
            
            // Checkbox за избор на всички
            this.bindSelectAllCheckbox();

            // Сортиране на колони
            this.bindColumnSorting();
        },

        // Свързване на бутоните за действия
        bindActionButtons: function() {
            // Бутон за добавяне
            const addButton = document.getElementById('button-add');
            if (addButton) {
                addButton.addEventListener('click', () => {
                    window.location = addButton.getAttribute('data-href');
                });
            }

            // Бутони за редактиране
            document.querySelectorAll('.btn-edit').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    window.location = button.getAttribute('href');
                });
            });

            // Бутони за изтриване
            document.querySelectorAll('.btn-delete').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.confirmDelete(button.getAttribute('data-id'));
                });
            });

            // Bulk изтриване
            const deleteButton = document.getElementById('button-delete');
            if (deleteButton) {
                deleteButton.addEventListener('click', () => {
                    this.confirmBulkDelete();
                });
            }
        },

        // Свързване на checkbox за избор на всички
        bindSelectAllCheckbox: function() {
            const selectAllCheckbox = document.getElementById('select-all');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', (e) => {
                    const checkboxes = document.querySelectorAll('input[name="selected[]"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = e.target.checked;
                    });
                    this.updateBulkActionButtons();
                });
            }

            // Индивидуални checkboxes
            document.querySelectorAll('input[name="selected[]"]').forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    this.updateSelectAllState();
                    this.updateBulkActionButtons();
                });
            });
        },

        // Свързване на сортирането на колони
        bindColumnSorting: function() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', (e) => {
                    e.preventDefault();
                    const sort = header.getAttribute('data-sort');
                    const currentOrder = header.getAttribute('data-order') || 'ASC';
                    const newOrder = currentOrder === 'ASC' ? 'DESC' : 'ASC';
                    
                    this.sortTable(sort, newOrder);
                });
            });
        },

        // Инициализация на филтрите
        initializeFilters: function() {
            // Запазване на текущите филтри в localStorage
            this.saveFiltersToStorage();
            
            // Възстановяване на филтри от localStorage
            this.restoreFiltersFromStorage();
        },

        // Инициализация на търсенето
        initializeSearch: function() {
            // Autocomplete за търсене
            const searchInput = document.getElementById('input-name');
            if (searchInput) {
                this.initializeAutocomplete(searchInput);
            }
        },

        // Инициализация на autocomplete
        initializeAutocomplete: function(input) {
            let autocompleteTimeout;
            let currentRequest = null;

            input.addEventListener('input', (e) => {
                clearTimeout(autocompleteTimeout);
                
                if (currentRequest) {
                    currentRequest.abort();
                }

                const value = e.target.value.trim();
                
                if (value.length < 2) {
                    this.hideAutocomplete();
                    return;
                }

                autocompleteTimeout = setTimeout(() => {
                    currentRequest = this.fetchAutocompleteData(value);
                }, 300);
            });

            // Скриване на autocomplete при click извън него
            document.addEventListener('click', (e) => {
                if (!input.contains(e.target)) {
                    this.hideAutocomplete();
                }
            });
        },

        // Получаване на autocomplete данни
        fetchAutocompleteData: function(query) {
            const url = `index.php?route=catalog/manufacturer/autocomplete&filter_name=${encodeURIComponent(query)}`;
            
            return this.makeRequest(url, 'GET')
                .then(data => {
                    this.showAutocomplete(data, query);
                })
                .catch(error => {
                    console.error('Autocomplete error:', error);
                });
        },

        // Показване на autocomplete резултати
        showAutocomplete: function(data, query) {
            let dropdown = document.getElementById('manufacturer-autocomplete');
            
            if (!dropdown) {
                dropdown = document.createElement('div');
                dropdown.id = 'manufacturer-autocomplete';
                dropdown.className = 'autocomplete-dropdown';
                document.body.appendChild(dropdown);
            }

            dropdown.innerHTML = '';

            if (data && data.length > 0) {
                data.forEach(item => {
                    const option = document.createElement('div');
                    option.className = 'autocomplete-option';
                    option.innerHTML = `
                        <div class="manufacturer-info">
                            ${item.image ? `<img src="${item.image}" alt="${item.name}" class="manufacturer-thumb">` : ''}
                            <span class="manufacturer-name">${this.highlightMatch(item.name, query)}</span>
                        </div>
                    `;
                    
                    option.addEventListener('click', () => {
                        document.getElementById('input-name').value = item.name;
                        this.hideAutocomplete();
                        this.performSearch(item.name);
                    });
                    
                    dropdown.appendChild(option);
                });

                this.positionAutocomplete(dropdown);
                dropdown.style.display = 'block';
            } else {
                this.hideAutocomplete();
            }
        },

        // Позициониране на autocomplete
        positionAutocomplete: function(dropdown) {
            const input = document.getElementById('input-name');
            const rect = input.getBoundingClientRect();
            
            dropdown.style.position = 'absolute';
            dropdown.style.top = (rect.bottom + window.scrollY) + 'px';
            dropdown.style.left = rect.left + 'px';
            dropdown.style.width = rect.width + 'px';
            dropdown.style.zIndex = '9999';
        },

        // Скриване на autocomplete
        hideAutocomplete: function() {
            const dropdown = document.getElementById('manufacturer-autocomplete');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        },

        // Подчертаване на съвпадения в текста
        highlightMatch: function(text, query) {
            const regex = new RegExp(`(${query})`, 'gi');
            return text.replace(regex, '<strong>$1</strong>');
        },

        // Изпълнение на търсене
        performSearch: function(query) {
            const url = new URL(window.location);
            
            if (query.trim()) {
                url.searchParams.set('filter_name', query);
            } else {
                url.searchParams.delete('filter_name');
            }
            
            url.searchParams.delete('page'); // Нулиране на страницата при ново търсене
            
            window.location = url.toString();
        },

        // Прилагане на филтри
        applyFilters: function() {
            const url = new URL(window.location);
            
            // Sort order филтър
            const sortOrder = document.getElementById('input-sort-order').value;
            if (sortOrder) {
                url.searchParams.set('filter_sort_order', sortOrder);
            } else {
                url.searchParams.delete('filter_sort_order');
            }
            
            url.searchParams.delete('page'); // Нулиране на страницата при нови филтри
            
            window.location = url.toString();
        },

        // Сортиране на таблицата
        sortTable: function(sort, order) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sort);
            url.searchParams.set('order', order);
            url.searchParams.delete('page'); // Нулиране на страницата при ново сортиране
            
            window.location = url.toString();
        },

        // Инициализация на bulk действия
        initializeBulkActions: function() {
            this.updateBulkActionButtons();
        },

        // Актуализиране на състоянието на "избери всички"
        updateSelectAllState: function() {
            const selectAllCheckbox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('input[name="selected[]"]');
            const checkedBoxes = document.querySelectorAll('input[name="selected[]"]:checked');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
                selectAllCheckbox.checked = checkedBoxes.length === checkboxes.length && checkboxes.length > 0;
            }
        },

        // Актуализиране на бутоните за bulk действия
        updateBulkActionButtons: function() {
            const checkedBoxes = document.querySelectorAll('input[name="selected[]"]:checked');
            const deleteButton = document.getElementById('button-delete');

            if (deleteButton) {
                deleteButton.disabled = checkedBoxes.length === 0;
            }
        },

        // Потвърждение за изтриване на един производител
        confirmDelete: function(manufacturerId) {
            if (confirm('Сигурни ли сте, че искате да изтриете този производител?')) {
                this.deleteManufacturer(manufacturerId);
            }
        },

        // Потвърждение за bulk изтриване
        confirmBulkDelete: function() {
            const checkedBoxes = document.querySelectorAll('input[name="selected[]"]:checked');
            if (checkedBoxes.length === 0) {
                alert('Моля, изберете производители за изтриване!');
                return;
            }

            if (confirm(`Сигурни ли сте, че искате да изтриете ${checkedBoxes.length} производители?`)) {
                this.bulkDeleteManufacturers();
            }
        },

        // Изтриване на производител
        deleteManufacturer: function(manufacturerId) {
            const url = `index.php?route=catalog/manufacturer/delete&manufacturer_id=${manufacturerId}`;

            this.makeRequest(url, 'POST')
                .then(data => {
                    if (data.success) {
                        this.showNotification(data.success, 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else if (data.error) {
                        this.showNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    this.showNotification('Възникна грешка при изтриването!', 'error');
                    console.error('Delete error:', error);
                });
        },

        // Bulk изтриване на производители
        bulkDeleteManufacturers: function() {
            const checkedBoxes = document.querySelectorAll('input[name="selected[]"]:checked');
            const selectedIds = Array.from(checkedBoxes).map(cb => cb.value);

            const formData = new FormData();
            selectedIds.forEach(id => {
                formData.append('selected[]', id);
            });

            const url = 'index.php?route=catalog/manufacturer/delete';

            this.makeRequest(url, 'POST', formData)
                .then(data => {
                    if (data.success) {
                        this.showNotification(data.success, 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else if (data.error) {
                        this.showNotification(data.error, 'error');
                    }

                    if (data.warnings && data.warnings.length > 0) {
                        data.warnings.forEach(warning => {
                            this.showNotification(warning, 'warning');
                        });
                    }
                })
                .catch(error => {
                    this.showNotification('Възникна грешка при изтриването!', 'error');
                    console.error('Bulk delete error:', error);
                });
        },

        // Показване на нотификация
        showNotification: function(message, type = 'info') {
            // Създаване на нотификация
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible`;
            notification.innerHTML = `
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                ${message}
            `;

            // Добавяне в контейнера за нотификации
            let container = document.getElementById('notification-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'notification-container';
                container.style.position = 'fixed';
                container.style.top = '20px';
                container.style.right = '20px';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
            }

            container.appendChild(notification);

            // Автоматично премахване след 5 секунди
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        // Инициализация на tooltips
        initializeTooltips: function() {
            // Ако има Bootstrap tooltips
            if (typeof $ !== 'undefined' && $.fn.tooltip) {
                $('[data-toggle="tooltip"]').tooltip();
            }
        },

        // Запазване на филтри в localStorage
        saveFiltersToStorage: function() {
            const filters = {
                filter_name: document.getElementById('input-name')?.value || '',
                filter_sort_order: document.getElementById('input-sort-order')?.value || ''
            };

            localStorage.setItem('manufacturer_filters', JSON.stringify(filters));
        },

        // Възстановяване на филтри от localStorage
        restoreFiltersFromStorage: function() {
            try {
                const savedFilters = localStorage.getItem('manufacturer_filters');
                if (savedFilters) {
                    const filters = JSON.parse(savedFilters);

                    if (filters.filter_name && document.getElementById('input-name')) {
                        document.getElementById('input-name').value = filters.filter_name;
                    }

                    if (filters.filter_sort_order && document.getElementById('input-sort-order')) {
                        document.getElementById('input-sort-order').value = filters.filter_sort_order;
                    }
                }
            } catch (e) {
                console.warn('Error restoring filters from localStorage:', e);
            }
        },

        // Помощна функция за HTTP заявки
        makeRequest: function(url, method = 'GET', data = null) {
            return new Promise((resolve, reject) => {
                // Проверка за максимален брой едновременни заявки
                if (this.config.activeRequests >= this.config.maxConcurrentRequests) {
                    this.config.requestQueue.push({ url, method, data, resolve, reject });
                    return;
                }

                this.config.activeRequests++;

                const xhr = new XMLHttpRequest();
                xhr.open(method, url);

                if (method === 'POST' && !(data instanceof FormData)) {
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                }

                xhr.onload = () => {
                    this.config.activeRequests--;
                    this.processRequestQueue();

                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                };

                xhr.onerror = () => {
                    this.config.activeRequests--;
                    this.processRequestQueue();
                    reject(new Error('Network error'));
                };

                xhr.send(data);
            });
        },

        // Обработка на опашката със заявки
        processRequestQueue: function() {
            if (this.config.requestQueue.length > 0 && this.config.activeRequests < this.config.maxConcurrentRequests) {
                const { url, method, data, resolve, reject } = this.config.requestQueue.shift();
                this.makeRequest(url, method, data).then(resolve).catch(reject);
            }
        }
    };

    // Инициализация при зареждане на DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            ManufacturerListing.init();
        });
    } else {
        ManufacturerListing.init();
    }

    // Експортиране за глобален достъп
    window.ManufacturerListing = ManufacturerListing;

})();
