/**
 * Option Listing Module
 * Управление на списъка с опции - интегриран в BackendModule
 */
(function() {
    'use strict';

    // Разширяване на BackendModule с функционалност за опции
    Object.assign(BackendModule, {
        
        // Конфигурация за опции
        optionListing: {
            config: {
                deleteUrl: 'index.php?route=catalog/option/delete',
                editUrl: 'index.php?route=catalog/option/edit',
                userToken: '',
                baseUrl: ''
            },

            /**
             * Инициализация на листването на опции
             */
            init: function() {
                console.log('Initializing option listing...');

                // Инициализация на конфигурацията
                if (typeof BackendModule !== 'undefined' && BackendModule.config) {
                    this.config.userToken = BackendModule.config.userToken;
                    this.config.baseUrl = BackendModule.config.baseUrl;
                } else {
                    console.warn('BackendModule not found, using fallback configuration');
                    // Fallback - опит за извличане на user_token от URL или други източници
                    const urlParams = new URLSearchParams(window.location.search);
                    this.config.userToken = urlParams.get('user_token') || '';
                    this.config.baseUrl = window.location.origin;
                }

                console.log('Option listing config:', this.config);

                this.bindEvents();
                this.initializeComponents();
            },

            /**
             * Свързване на събития
             */
            bindEvents: function() {
                this.bindFilterEvents();
                this.bindSelectionEvents();
                this.bindDeleteEvents();
                this.bindEditEvents();
                this.bindSortEvents();
                this.bindAutocompleteEvents();
            },

            /**
             * Свързване на събития за филтриране
             */
            bindFilterEvents: function() {
                const filterForm = document.getElementById('filter-form');
                const filterName = document.getElementById('filter-name');
                const filterType = document.getElementById('filter-type');
                const clearFilterBtn = document.getElementById('clear-filter');

                if (filterForm) {
                    filterForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.applyFilters();
                    });
                }

                if (filterName) {
                    let debounceTimer;
                    filterName.addEventListener('input', () => {
                        clearTimeout(debounceTimer);
                        debounceTimer = setTimeout(() => {
                            this.applyFilters();
                        }, 500);
                    });
                }

                if (filterType) {
                    filterType.addEventListener('change', () => {
                        this.applyFilters();
                    });
                }

                if (clearFilterBtn) {
                    clearFilterBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.clearFilters();
                    });
                }
            },

            /**
             * Свързване на събития за селектиране
             */
            bindSelectionEvents: function() {
                const selectAllCheckbox = document.getElementById('select-all');
                const itemCheckboxes = document.querySelectorAll('.item-checkbox');

                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', (e) => {
                        this.toggleSelectAll(e.target.checked);
                    });
                }

                itemCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => {
                        this.updateSelectAllState();
                        this.updateBulkActions();
                    });
                });
            },

            /**
             * Свързване на събития за изтриване
             */
            bindDeleteEvents: function() {
                const deleteSelectedBtn = document.getElementById('delete-selected');
                const deleteButtons = document.querySelectorAll('.delete-btn');
                const self = this;

                if (deleteSelectedBtn) {
                    deleteSelectedBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        self.deleteSelected();
                    });
                }

                deleteButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.preventDefault();
                        const optionId = btn.dataset.optionId;
                        const optionName = btn.dataset.optionName;
                        self.deleteSingle(optionId, optionName);
                    });
                });
            },

            /**
             * Свързване на събития за редактиране
             */
            bindEditEvents: function() {
                const editButtons = document.querySelectorAll('.edit-btn');

                editButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.preventDefault();
                        const editUrl = btn.href || btn.dataset.editUrl;
                        if (editUrl) {
                            console.log('Navigating to edit URL:', editUrl);
                            window.location.href = editUrl;
                        } else {
                            console.error('Edit URL not found for button:', btn);
                        }
                    });
                });
            },

            /**
             * Свързване на събития за сортиране
             */
            bindSortEvents: function() {
                const sortHeaders = document.querySelectorAll('.sort-header');
                const self = this;

                sortHeaders.forEach(header => {
                    header.addEventListener('click', (e) => {
                        e.preventDefault();
                        const sortField = header.dataset.sort;
                        self.applySorting(sortField);
                    });
                });
            },

            /**
             * Свързване на autocomplete събития
             */
            bindAutocompleteEvents: function() {
                const filterNameInput = document.getElementById('filter-name');
                if (!filterNameInput) return;

                console.log('Setting up autocomplete for option search');

                // Създаване на dropdown контейнер
                this.createAutocompleteDropdown(filterNameInput);

                const self = this;
                let debounceTimer;

                // Focus event - показване на първите 10 опции
                filterNameInput.addEventListener('focus', () => {
                    console.log('Filter input focused, showing autocomplete');
                    self.showAutocompleteDropdown(filterNameInput, '');
                });

                // Input event - търсене с debounce
                filterNameInput.addEventListener('input', (e) => {
                    console.log('Filter input changed:', e.target.value);
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        self.showAutocompleteDropdown(filterNameInput, e.target.value);
                    }, 300);
                });

                // Blur event - скриване на dropdown (с малко забавяне за клик)
                filterNameInput.addEventListener('blur', () => {
                    setTimeout(() => {
                        self.hideAutocompleteDropdown(filterNameInput);
                    }, 200);
                });

                // Escape key - скриване на dropdown
                filterNameInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        self.hideAutocompleteDropdown(filterNameInput);
                    }
                });
            },

            /**
             * Инициализация на компоненти
             */
            initializeComponents: function() {
                this.updateSelectAllState();
                this.updateBulkActions();
            },

            /**
             * Прилагане на филтри
             */
            applyFilters: function() {
                const filterName = document.getElementById('filter-name')?.value || '';
                const filterType = document.getElementById('filter-type')?.value || '';

                console.log('Applying filters - Name:', filterName, 'Type:', filterType);

                const params = new URLSearchParams();
                params.append('user_token', this.config.userToken);

                if (filterName) {
                    params.append('filter_name', filterName);
                }

                if (filterType) {
                    params.append('filter_type', filterType);
                }

                // Запазване на текущата страница ако няма промяна в филтрите
                const currentUrl = new URL(window.location.href);
                const currentFilterName = currentUrl.searchParams.get('filter_name') || '';
                const currentFilterType = currentUrl.searchParams.get('filter_type') || '';

                // Ако филтрите са променени, започваме от страница 1
                if (filterName !== currentFilterName || filterType !== currentFilterType) {
                    params.append('page', '1');
                } else {
                    const currentPage = currentUrl.searchParams.get('page') || '1';
                    params.append('page', currentPage);
                }

                const newUrl = `index.php?route=catalog/option&${params.toString()}`;
                console.log('Redirecting to:', newUrl);
                window.location.href = newUrl;
            },

            /**
             * Изчистване на филтри
             */
            clearFilters: function() {
                window.location.href = `index.php?route=catalog/option&user_token=${BackendModule.config.userToken}`;
            },

            /**
             * Превключване на селектиране на всички
             */
            toggleSelectAll: function(checked) {
                const itemCheckboxes = document.querySelectorAll('.item-checkbox');
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = checked;
                });
                this.updateBulkActions();
            },

            /**
             * Актуализиране на състоянието на "Избери всички"
             */
            updateSelectAllState: function() {
                const selectAllCheckbox = document.getElementById('select-all');
                const itemCheckboxes = document.querySelectorAll('.item-checkbox');
                
                if (!selectAllCheckbox || itemCheckboxes.length === 0) return;

                const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
                
                selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
            },

            /**
             * Актуализиране на масовите действия
             */
            updateBulkActions: function() {
                const deleteSelectedBtn = document.getElementById('delete-selected');
                const selectedCount = document.querySelectorAll('.item-checkbox:checked').length;

                if (deleteSelectedBtn) {
                    deleteSelectedBtn.disabled = selectedCount === 0;
                    deleteSelectedBtn.textContent = selectedCount > 0 ? 
                        `Изтрий избраните (${selectedCount})` : 'Изтрий избраните';
                }
            },

            /**
             * Изтриване на избраните опции
             */
            deleteSelected: function() {
                const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
                
                if (selectedCheckboxes.length === 0) {
                    BackendModule.showAlert('warning', 'Моля, изберете опции за изтриване');
                    return;
                }

                const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
                const confirmMessage = `Сигурни ли сте, че искате да изтриете ${selectedIds.length} опции?`;

                if (confirm(confirmMessage)) {
                    this.performDelete(selectedIds);
                }
            },

            /**
             * Изтриване на единична опция
             */
            deleteSingle: function(optionId, optionName) {
                const confirmMessage = `Сигурни ли сте, че искате да изтриете опцията "${optionName}"?`;

                if (confirm(confirmMessage)) {
                    this.performDelete([optionId]);
                }
            },

            /**
             * Извършване на изтриване
             */
            performDelete: function(optionIds) {
                const formData = new FormData();
                formData.append('user_token', BackendModule.config.userToken);
                
                optionIds.forEach(id => {
                    formData.append('selected[]', id);
                });

                fetch(this.config.deleteUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        BackendModule.showAlert('success', data.success);
                        window.location.reload();
                    } else if (data.error) {
                        BackendModule.showAlert('error', data.error);
                    } else if (data.warning) {
                        BackendModule.showAlert('warning', data.warning);
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Грешка при изтриване:', error);
                    BackendModule.showAlert('error', 'Възникна грешка при изтриване на опциите');
                });
            },

            /**
             * Прилагане на сортиране
             */
            applySorting: function(sortField) {
                const currentUrl = new URL(window.location.href);
                const currentSort = currentUrl.searchParams.get('sort');
                const currentOrder = currentUrl.searchParams.get('order');

                let newOrder = 'ASC';
                if (currentSort === sortField && currentOrder === 'ASC') {
                    newOrder = 'DESC';
                }

                currentUrl.searchParams.set('sort', sortField);
                currentUrl.searchParams.set('order', newOrder);

                window.location.href = currentUrl.toString();
            },

            /**
             * Създаване на autocomplete dropdown
             */
            createAutocompleteDropdown: function(input) {
                const existingDropdown = input.parentNode.querySelector('.autocomplete-dropdown');
                if (existingDropdown) return;

                const dropdown = document.createElement('div');
                dropdown.className = 'autocomplete-dropdown absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto hidden';
                dropdown.innerHTML = '<div class="p-2 text-sm text-gray-500">Зареждане...</div>';
                
                input.parentNode.style.position = 'relative';
                input.parentNode.appendChild(dropdown);
            },

            /**
             * Показване на autocomplete dropdown
             */
            showAutocompleteDropdown: function(input, query) {
                const dropdown = input.parentNode.querySelector('.autocomplete-dropdown');
                if (!dropdown) return;

                console.log('Showing autocomplete dropdown for query:', query);

                // Показване на loading
                dropdown.innerHTML = '<div class="p-2 text-sm text-gray-500 flex items-center"><i class="ri-loader-4-line animate-spin mr-2"></i>Зареждане...</div>';
                dropdown.classList.remove('hidden');

                // AJAX заявка
                const params = new URLSearchParams();
                params.append('user_token', this.config.userToken);
                params.append('filter_name', query);
                params.append('limit', '10');

                const filterType = document.getElementById('filter-type');
                if (filterType && filterType.value) {
                    params.append('filter_type', filterType.value);
                    console.log('Adding filter_type:', filterType.value);
                }

                const self = this;
                const url = `index.php?route=catalog/option/ajaxSearch&${params.toString()}`;
                console.log('Making AJAX request to:', url);

                fetch(url)
                    .then(response => {
                        console.log('AJAX response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('AJAX response data:', data);
                        self.renderAutocompleteResults(dropdown, data, input);
                    })
                    .catch(error => {
                        console.error('Autocomplete error:', error);
                        dropdown.innerHTML = '<div class="p-2 text-sm text-red-500">Грешка при зареждане</div>';
                    });
            },

            /**
             * Рендиране на autocomplete резултати
             */
            renderAutocompleteResults: function(dropdown, data, input) {
                console.log('Rendering autocomplete results:', data);

                if (data.error) {
                    console.error('Autocomplete error:', data.error);
                    dropdown.innerHTML = `<div class="p-2 text-sm text-red-500">${data.error}</div>`;
                    return;
                }

                if (!Array.isArray(data) || data.length === 0) {
                    console.log('No options found');
                    dropdown.innerHTML = '<div class="p-2 text-sm text-gray-500">Няма намерени опции</div>';
                    return;
                }

                console.log('Rendering', data.length, 'options');

                const self = this;
                let html = '';
                data.forEach(option => {
                    html += `
                        <div class="autocomplete-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                             data-option-id="${option.option_id}"
                             data-option-name="${option.name}">
                            <div class="font-medium text-gray-900">${option.name}</div>
                            <div class="text-xs text-gray-500">${option.type_text}</div>
                        </div>
                    `;
                });

                dropdown.innerHTML = html;

                // Добавяне на click events
                dropdown.querySelectorAll('.autocomplete-item').forEach(item => {
                    item.addEventListener('click', () => {
                        console.log('Autocomplete item clicked:', item.dataset.optionName);
                        self.selectAutocompleteItem(item, input);
                    });
                });
            },

            /**
             * Избор на autocomplete елемент
             */
            selectAutocompleteItem: function(item, input) {
                const optionName = item.dataset.optionName;
                console.log('Selected autocomplete item:', optionName);

                // Задаване на стойността в полето
                input.value = optionName;

                // Скриване на dropdown
                this.hideAutocompleteDropdown(input);

                // Автоматично прилагане на филтъра
                console.log('Applying filters after autocomplete selection');
                this.applyFilters();
            },

            /**
             * Скриване на autocomplete dropdown
             */
            hideAutocompleteDropdown: function(input) {
                const dropdown = input.parentNode.querySelector('.autocomplete-dropdown');
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
            }
        }
    });

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        if (document.querySelector('.option-listing-page')) {
            BackendModule.optionListing.init();
        }
    });

})();
