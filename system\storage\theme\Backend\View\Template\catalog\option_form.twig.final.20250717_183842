

<!-- Option Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
                <div class="w-8 h-8 flex items-center justify-center">
                    <i class="ri-arrow-left-line ri-lg"></i>
                </div>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            {% if option_id > 0 and delete_url %}
            <button type="button" id="delete-option" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors">
                <i class="ri-delete-bin-line mr-2"></i>
                Изтрий
            </button>
            {% endif %}
            <button type="submit" form="option-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <i class="ri-save-line mr-2"></i>
                Запази
            </button>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <form id="option-form" class="space-y-6">
        <input type="hidden" name="option_id" value="{{ option_id }}">

        <div class="max-w-4xl">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">

                <!-- Form Content -->
                <div class="p-6">

                    <!-- Basic Information Section -->
                    <div class="space-y-6">

                        <!-- Option Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Тип опция <span class="text-red-500">*</span>
                            </label>
                            <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                                <option value="">Изберете тип опция</option>
                                {% for type_key, type_name in option_types %}
                                <option value="{{ type_key }}" {% if type_key == type %}selected{% endif %}>
                                    {{ type_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                            <input type="number" name="sort_order" value="{{ sort_order }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                                   placeholder="0">
                            <p class="text-xs text-gray-500 mt-1">По-ниските числа се показват първи</p>
                        </div>

                        <!-- Language Tabs -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Имена на опцията</h3>

                            <!-- Language Tab Headers -->
                            <div class="border-b border-gray-200 mb-4">
                                <nav class="-mb-px flex space-x-8">
                                    {% for language in languages %}
                                    <button type="button" class="language-tab py-2 px-1 border-b-2 font-medium text-sm {% if loop.first %}border-primary text-primary{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}"
                                            data-language-id="{{ language.language_id }}">
                                        <div class="flex items-center">
                                            <div class="w-6 h-4 flex items-center justify-center {{ language.css }} rounded text-xs font-medium text-white mr-2">
                                                {{ language.code|upper|slice(0, 2) }}
                                            </div>
                                            {{ language.name }}
                                        </div>
                                    </button>
                                    {% endfor %}
                                </nav>
                            </div>

                            <!-- Language Tab Content -->
                            {% for language in languages %}
                            <div id="language-panel-{{ language.language_id }}" class="language-panel {% if not loop.first %}hidden{% endif %}">
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Име на опцията <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" name="option_description[{{ language.language_id }}][name]"
                                               value="{{ option_description[language.language_id].name }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                                               placeholder="Въведете име на опцията за {{ language.name }}"
                                               maxlength="128">
                                        <p class="text-xs text-gray-500 mt-1">Максимум 128 символа</p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Option Values Section (for select, radio, checkbox types) -->
                        <div id="option-values-section" class="border-t border-gray-200 pt-6 hidden">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Стойности на опцията</h3>
                                <button type="button"
                                        id="add-option-value"
                                        class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors flex items-center">
                                    <i class="ri-add-line mr-2"></i>
                                    Добави стойност
                                </button>
                            </div>

                            <div id="option-values-container" class="space-y-4">
                                {% if option_values %}
                                    {% for key, option_value in option_values %}
                                        <div class="option-value-item bg-gray-50 p-4 rounded border border-gray-200">
                                            <div class="flex justify-between items-center mb-3">
                                                <h4 class="text-sm font-medium text-gray-900">Стойност {{ key + 1 }}</h4>
                                                <button type="button" class="remove-value-btn text-red-600 hover:text-red-800 transition-colors">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div class="space-y-4">
                                                    {% for language in languages %}
                                                        <div class="value-language-panel">
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                                                Име ({{ language.name }})
                                                            </label>
                                                            <input type="text"
                                                                   name="option_value[{{ key }}][option_value_description][{{ language.language_id }}][name]"
                                                                   value="{{ option_value.option_value_description[language.language_id].name }}"
                                                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                                                                   placeholder="Въведете име на стойността">
                                                        </div>
                                                    {% endfor %}
                                                </div>

                                                <div class="space-y-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">Изображение</label>
                                                        <div class="relative group w-20 h-20">
                                                            <div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
                                                                <img src="{{ option_value.thumb|default(placeholder) }}" alt="Option image" class="w-full h-full object-cover">
                                                            </div>
                                                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                                                <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Избери от библиотеката" data-action="select" data-value-index="{{ key }}">
                                                                    <div class="w-5 h-5 flex items-center justify-center">
                                                                        <i class="ri-folder-image-line"></i>
                                                                    </div>
                                                                </button>
                                                                <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни" data-action="remove" data-value-index="{{ key }}">
                                                                    <div class="w-5 h-5 flex items-center justify-center">
                                                                        <i class="ri-delete-bin-line"></i>
                                                                    </div>
                                                                </button>
                                                            </div>
                                                            <input type="hidden" name="option_value[{{ key }}][image]" value="{{ option_value.image }}" id="input-image-{{ key }}">
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                                                        <input type="number"
                                                               name="option_value[{{ key }}][sort_order]"
                                                               value="{{ option_value.sort_order }}"
                                                               class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                                                               placeholder="0">
                                                    </div>
                                                </div>
                                            </div>

                                            <input type="hidden" name="option_value[{{ key }}][option_value_id]" value="{{ option_value.option_value_id }}">
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>

                            {% if not option_values %}
                                <div class="text-center py-8 text-gray-500">
                                    <i class="ri-add-circle-line text-3xl mb-2"></i>
                                    <p>Няма добавени стойности. Натиснете "Добави стойност" за да започнете.</p>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Help Text -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-information-line text-blue-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">Информация за опциите</h3>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>Опциите се използват за персонализиране на продуктите от клиентите</li>
                                            <li>Типовете Select, Radio и Checkbox изискват дефиниране на стойности</li>
                                            <li>Типовете Text, Textarea, File, Date, Time и DateTime не използват предефинирани стойности</li>
                                            <li>Подредбата определя реда на показване в продуктовите форми</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</main>

<script>
// Обработка на изтриване на опция
document.addEventListener('DOMContentLoaded', function() {
    const deleteBtn = document.getElementById('delete-option');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('Сигурни ли сте, че искате да изтриете тази опция?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ delete_url }}';

                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = 'user_token';
                tokenInput.value = '{{ user_token }}';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'option_id';
                idInput.value = '{{ option_id }}';

                form.appendChild(tokenInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
});
</script>
